name = "crebost-landing"
compatibility_date = "2024-01-15"
pages_build_output_dir = "dist"

[env.production]
name = "crebost-landing"
route = { pattern = "landing.crebost.com/*", zone_name = "crebost.com" }

[env.production.vars]
NODE_ENV = "production"
NEXT_PUBLIC_AUTH_URL = "https://auth.crebost.com"
NEXT_PUBLIC_DASHBOARD_URL = "https://dashboard.crebost.com"
NEXT_PUBLIC_ADMIN_URL = "https://admin.crebost.com"

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "YOUR_KV_NAMESPACE_ID"
preview_id = "YOUR_PREVIEW_KV_NAMESPACE_ID"

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.upload]
format = "modules"
