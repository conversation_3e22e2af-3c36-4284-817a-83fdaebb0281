@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom button styles */
.btn-primary {
  @apply bg-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-200;
}

.btn-secondary {
  @apply border border-indigo-600 text-indigo-600 px-6 py-3 rounded-lg font-semibold hover:bg-indigo-50 transition-colors duration-200;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
}

/* Custom form styles */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* Hero section background */
.hero-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Section spacing */
.section-padding {
  @apply py-16 lg:py-24;
}

/* Container max width */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Responsive text sizes */
.text-hero {
  @apply text-4xl sm:text-5xl lg:text-6xl font-bold;
}

.text-section-title {
  @apply text-3xl sm:text-4xl lg:text-5xl font-bold;
}

.text-subsection-title {
  @apply text-xl sm:text-2xl lg:text-3xl font-semibold;
}

/* Grid responsive */
.grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8;
}

/* Feature card styles */
.feature-card {
  @apply bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
}

.feature-icon {
  @apply w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4;
}

/* Pricing card styles */
.pricing-card {
  @apply bg-white border border-gray-200 rounded-lg p-8 relative;
}

.pricing-card.featured {
  @apply border-2 border-indigo-600 transform scale-105;
}

/* CTA section */
.cta-section {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white;
}

/* Footer styles */
.footer-link {
  @apply text-gray-400 hover:text-white transition-colors duration-200;
}

/* Mobile menu */
.mobile-menu {
  @apply md:hidden absolute top-16 left-0 right-0 bg-white border-t border-gray-200 shadow-lg;
}

/* Smooth transitions */
.transition-smooth {
  @apply transition-all duration-300 ease-in-out;
}
